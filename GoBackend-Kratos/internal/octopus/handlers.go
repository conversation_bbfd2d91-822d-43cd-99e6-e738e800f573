package octopus

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"runtime"
	"time"

	"github.com/gorilla/mux"
	"github.com/gorilla/websocket"
)

// 🌐 Setup HTTP Server with Octopus Routes
func (o *MorphicOctopusInterface) setupHTTPServer() {
	router := mux.NewRouter()

	// Enable CORS
	router.Use(o.corsMiddleware)
	router.Use(o.loggingMiddleware)

	// Dashboard routes
	router.HandleFunc("/", o.handleDashboard).Methods("GET")
	router.HandleFunc("/dashboard", o.handleDashboard).Methods("GET")
	router.HandleFunc("/api/dashboard/data", o.handleDashboardData).Methods("GET")
	router.HandleFunc("/api/dashboard/ws", o.handleWebSocket)

	// System management routes
	o.setupSystemRoutes(router)

	// Service management routes
	o.setupServiceRoutes(router)

	// Customer intelligence routes
	o.setupCustomerRoutes(router)

	// Transcription management routes
	o.setupTranscriptionRoutes(router)

	// Email intelligence routes
	o.setupEmailRoutes(router)

	// AI management routes
	o.setupAIRoutes(router)

	// Static files
	router.PathPrefix("/static/").Handler(http.StripPrefix("/static/",
		http.FileServer(http.Dir("./web/octopus/static/"))))

	o.httpServer = &http.Server{
		Addr:         fmt.Sprintf(":%d", o.config.HTTPPort),
		Handler:      router,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	o.log.Infof("🐙 Morphic Octopus Interface configured on port %d", o.config.HTTPPort)
}

// 🎛️ Setup System Management Routes
func (o *MorphicOctopusInterface) setupSystemRoutes(router *mux.Router) {
	api := router.PathPrefix("/api/system").Subrouter()

	api.HandleFunc("/status", o.handleSystemStatus).Methods("GET")
	api.HandleFunc("/health", o.handleSystemHealth).Methods("GET")
	api.HandleFunc("/metrics", o.handleSystemMetrics).Methods("GET")
	api.HandleFunc("/logs", o.handleSystemLogs).Methods("GET")
	api.HandleFunc("/restart", o.handleSystemRestart).Methods("POST")
	api.HandleFunc("/backup", o.handleSystemBackup).Methods("POST")
	api.HandleFunc("/maintenance", o.handleMaintenanceMode).Methods("POST")
}

// 🔧 Setup Service Management Routes
func (o *MorphicOctopusInterface) setupServiceRoutes(router *mux.Router) {
	api := router.PathPrefix("/api/services").Subrouter()

	api.HandleFunc("/health", o.handleServicesHealth).Methods("GET")
	api.HandleFunc("/{service}/start", o.handleServiceStart).Methods("POST")
	api.HandleFunc("/{service}/stop", o.handleServiceStop).Methods("POST")
	api.HandleFunc("/{service}/restart", o.handleServiceRestart).Methods("POST")
	api.HandleFunc("/{service}/config", o.handleServiceConfig).Methods("GET", "PUT")
	api.HandleFunc("/{service}/logs", o.handleServiceLogs).Methods("GET")
}

// 👥 Setup Customer Intelligence Routes
func (o *MorphicOctopusInterface) setupCustomerRoutes(router *mux.Router) {
	api := router.PathPrefix("/api/customers").Subrouter()

	api.HandleFunc("/metrics", o.handleCustomerMetrics).Methods("GET")
	api.HandleFunc("/segments", o.handleCustomerSegments).Methods("GET", "POST")
	api.HandleFunc("/intelligence/{id}", o.handleCustomerIntelligence).Methods("GET")
	api.HandleFunc("/search", o.handleCustomerSearch).Methods("POST")
	api.HandleFunc("/analytics/refresh", o.handleCustomerAnalyticsRefresh).Methods("POST")
	api.HandleFunc("/export", o.handleCustomerExport).Methods("POST")
}

// 📞 Setup Transcription Management Routes
func (o *MorphicOctopusInterface) setupTranscriptionRoutes(router *mux.Router) {
	api := router.PathPrefix("/api/transcription").Subrouter()

	api.HandleFunc("/stats", o.handleTranscriptionStats).Methods("GET")
	api.HandleFunc("/sources", o.handleTranscriptionSources).Methods("GET", "POST")
	api.HandleFunc("/sources/{id}", o.handleTranscriptionSource).Methods("GET", "PUT", "DELETE")
	api.HandleFunc("/calls", o.handleTranscriptionCalls).Methods("GET")
	api.HandleFunc("/calls/{id}", o.handleTranscriptionCall).Methods("GET")
	api.HandleFunc("/calls/{id}/reprocess", o.handleTranscriptionReprocess).Methods("POST")
	api.HandleFunc("/process", o.handleTranscriptionProcess).Methods("POST")
}

// 📧 Setup Email Intelligence Routes
func (o *MorphicOctopusInterface) setupEmailRoutes(router *mux.Router) {
	api := router.PathPrefix("/api/email").Subrouter()

	api.HandleFunc("/intelligence", o.handleEmailIntelligence).Methods("GET")
	api.HandleFunc("/campaigns", o.handleEmailCampaigns).Methods("GET", "POST")
	api.HandleFunc("/templates", o.handleEmailTemplates).Methods("GET", "POST")
	api.HandleFunc("/analytics", o.handleEmailAnalytics).Methods("GET")
	api.HandleFunc("/mailboxes", o.handleEmailMailboxes).Methods("GET", "POST")
	api.HandleFunc("/send", o.handleEmailSend).Methods("POST")
}

// 🤖 Setup AI Management Routes
func (o *MorphicOctopusInterface) setupAIRoutes(router *mux.Router) {
	api := router.PathPrefix("/api/ai").Subrouter()

	api.HandleFunc("/performance", o.handleAIPerformance).Methods("GET")
	api.HandleFunc("/models", o.handleAIModels).Methods("GET")
	api.HandleFunc("/models/{model}/status", o.handleAIModelStatus).Methods("GET")
	api.HandleFunc("/models/{model}/restart", o.handleAIModelRestart).Methods("POST")
	api.HandleFunc("/analyze", o.handleAIAnalyze).Methods("POST")
	api.HandleFunc("/train", o.handleAITrain).Methods("POST")
	api.HandleFunc("/queue", o.handleAIQueue).Methods("GET")
}

// 🚀 Start the Morphic Octopus Interface
func (o *MorphicOctopusInterface) Start(ctx context.Context) error {
	o.log.WithContext(ctx).Info("🐙 Starting Morphic Octopus Interface...")

	// Start real-time data broadcasting
	if o.config.WebSocketEnabled {
		go o.startRealtimeBroadcast(ctx)
	}

	// Start HTTP server
	go func() {
		o.log.Infof("🌐 Octopus Interface listening on port %d", o.config.HTTPPort)
		if err := o.httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			o.log.Errorf("❌ Octopus HTTP server error: %v", err)
		}
	}()

	o.log.WithContext(ctx).Info("✅ Morphic Octopus Interface started successfully!")
	return nil
}

// 🛑 Stop the Morphic Octopus Interface
func (o *MorphicOctopusInterface) Stop(ctx context.Context) error {
	o.log.WithContext(ctx).Info("🐙 Stopping Morphic Octopus Interface...")

	// Close WebSocket connections
	for id, conn := range o.wsConnections {
		conn.Close()
		delete(o.wsConnections, id)
	}

	// Stop HTTP server
	if o.httpServer != nil {
		ctx, cancel := context.WithTimeout(ctx, 30*time.Second)
		defer cancel()

		if err := o.httpServer.Shutdown(ctx); err != nil {
			o.log.WithContext(ctx).Errorf("❌ Octopus HTTP server shutdown error: %v", err)
		}
	}

	o.log.WithContext(ctx).Info("✅ Morphic Octopus Interface stopped")
	return nil
}

// 📊 Handle Dashboard Data Request
func (o *MorphicOctopusInterface) handleDashboardData(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	dashboard, err := o.buildDashboardData(ctx)
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to build dashboard data: %v", err), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(dashboard); err != nil {
		o.log.Errorf("Failed to encode dashboard data: %v", err)
	}
}

// 🏠 Handle Dashboard HTML
func (o *MorphicOctopusInterface) handleDashboard(w http.ResponseWriter, r *http.Request) {
	dashboardHTML := o.generateDashboardHTML()
	w.Header().Set("Content-Type", "text/html")
	w.Write([]byte(dashboardHTML))
}

// 🔌 Handle WebSocket Connection
func (o *MorphicOctopusInterface) handleWebSocket(w http.ResponseWriter, r *http.Request) {
	conn, err := o.wsUpgrader.Upgrade(w, r, nil)
	if err != nil {
		o.log.Errorf("WebSocket upgrade failed: %v", err)
		return
	}

	// Generate connection ID
	connID := fmt.Sprintf("ws_%d", time.Now().UnixNano())

	// Add connection to tracking
	o.addWebSocketConnection(connID, conn)

	o.log.Infof("🔌 New WebSocket connection: %s", connID)

	// Handle connection in goroutine
	go o.handleWebSocketConnection(connID, conn)
}

// handleWebSocketConnection manages individual WebSocket connections
func (o *MorphicOctopusInterface) handleWebSocketConnection(connID string, conn *websocket.Conn) {
	defer func() {
		o.removeWebSocketConnection(connID)
		o.log.Infof("🔌 WebSocket connection closed: %s", connID)
	}()

	// Send initial dashboard data
	dashboardData, err := o.buildDashboardData(context.Background())
	if err != nil {
		o.log.Errorf("Failed to build dashboard data: %v", err)
		return
	}

	if err := conn.WriteJSON(dashboardData); err != nil {
		o.log.Errorf("Failed to send initial data: %v", err)
		return
	}

	// Send periodic updates
	ticker := time.NewTicker(o.config.RefreshInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			// Send updated dashboard data
			dashboardData, err := o.buildDashboardData(context.Background())
			if err != nil {
				o.log.Errorf("Failed to build dashboard data: %v", err)
				continue
			}

			if err := conn.WriteJSON(dashboardData); err != nil {
				o.log.Errorf("Failed to send update: %v", err)
				return
			}
		}
	}
}
