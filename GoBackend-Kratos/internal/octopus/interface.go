package octopus

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/gorilla/mux"
	"github.com/gorilla/websocket"
	"gorm.io/gorm"

	"gobackend-hvac-kratos/internal/ai"
	"gobackend-hvac-kratos/internal/customer"
	"gobackend-hvac-kratos/internal/email"
	"gobackend-hvac-kratos/internal/transcription"
)

// 🐙 MORPHIC OCTOPUS INTERFACE - Potężny system zarządzania backendem
type MorphicOctopusInterface struct {
	log                *log.Helper
	db                 *gorm.DB
	httpServer         *http.Server
	wsUpgrader         websocket.Upgrader

	// Service Tentacles (Macki Usług)
	emailService       *email.EmailIntelligenceService
	transcriptionService *transcription.TranscriptionParser
	customerService    *customer.CustomerIntelligenceService
	aiService          *ai.Gemma3Service

	// 🔒 Thread-safe WebSocket connections with proper cleanup
	wsConnections      map[string]*websocket.Conn
	wsConnectionsMutex sync.RWMutex
	wsCleanupTicker    *time.Ticker

	// 📊 LangFuse-like Flow Tracking
	flowTracker        *FlowTracker
	requestMetrics     *RequestMetrics
	tokenUsage         *TokenUsageTracker

	// Configuration
	config             *OctopusConfig
}

// ⚙️ Octopus Configuration
type OctopusConfig struct {
	HTTPPort           int    `yaml:"http_port"`
	WebSocketEnabled   bool   `yaml:"websocket_enabled"`
	DashboardPath      string `yaml:"dashboard_path"`
	AuthEnabled        bool   `yaml:"auth_enabled"`
	AdminUsers         []string `yaml:"admin_users"`
	RefreshInterval    time.Duration `yaml:"refresh_interval"`
	MaxConnections     int    `yaml:"max_connections"`
}

// 📊 Octopus Dashboard Data
type OctopusDashboard struct {
	SystemStatus       *SystemStatus       `json:"system_status"`
	ServiceHealth      *ServiceHealth      `json:"service_health"`
	CustomerMetrics    *CustomerMetrics    `json:"customer_metrics"`
	TranscriptionStats *TranscriptionStats `json:"transcription_stats"`
	EmailIntelligence  *EmailIntelligence  `json:"email_intelligence"`
	AIPerformance      *AIPerformance      `json:"ai_performance"`
	RealtimeAlerts     []*Alert           `json:"realtime_alerts"`
	QuickActions       []*QuickAction     `json:"quick_actions"`
	Timestamp          time.Time          `json:"timestamp"`
}

// 🔧 System Status
type SystemStatus struct {
	Uptime             time.Duration `json:"uptime"`
	CPUUsage           float64       `json:"cpu_usage"`
	MemoryUsage        float64       `json:"memory_usage"`
	DatabaseConnections int          `json:"database_connections"`
	ActiveWebSockets   int           `json:"active_websockets"`
	TotalRequests      int64         `json:"total_requests"`
	ErrorRate          float64       `json:"error_rate"`
	ResponseTime       time.Duration `json:"avg_response_time"`
}

// 🏥 Service Health
type ServiceHealth struct {
	EmailService       *ServiceStatus `json:"email_service"`
	TranscriptionService *ServiceStatus `json:"transcription_service"`
	CustomerService    *ServiceStatus `json:"customer_service"`
	AIService          *ServiceStatus `json:"ai_service"`
	DatabaseService    *ServiceStatus `json:"database_service"`
	RedisService       *ServiceStatus `json:"redis_service"`
}

// 📈 Service Status
type ServiceStatus struct {
	Status             string        `json:"status"` // healthy, degraded, unhealthy
	LastCheck          time.Time     `json:"last_check"`
	ResponseTime       time.Duration `json:"response_time"`
	ErrorCount         int           `json:"error_count"`
	SuccessRate        float64       `json:"success_rate"`
	Message            string        `json:"message,omitempty"`
}

// 👥 Customer Metrics
type CustomerMetrics struct {
	TotalCustomers     int     `json:"total_customers"`
	NewToday           int     `json:"new_today"`
	NewThisWeek        int     `json:"new_this_week"`
	ActiveCustomers    int     `json:"active_customers"`
	HighValueCustomers int     `json:"high_value_customers"`
	AtRiskCustomers    int     `json:"at_risk_customers"`
	AvgSatisfaction    float64 `json:"avg_satisfaction"`
	ChurnRate          float64 `json:"churn_rate"`
	LifetimeValue      float64 `json:"avg_lifetime_value"`
}

// 📞 Transcription Statistics
type TranscriptionStats struct {
	TotalCalls         int     `json:"total_calls"`
	CallsToday         int     `json:"calls_today"`
	CallsThisWeek      int     `json:"calls_this_week"`
	HVACRelevantCalls  int     `json:"hvac_relevant_calls"`
	EmergencyCalls     int     `json:"emergency_calls"`
	AvgCallDuration    time.Duration `json:"avg_call_duration"`
	AvgConfidence      float64 `json:"avg_confidence"`
	ProcessingBacklog  int     `json:"processing_backlog"`
	TopCallerCompanies []string `json:"top_caller_companies"`
}

// 📧 Email Intelligence
type EmailIntelligence struct {
	TotalEmails        int     `json:"total_emails"`
	EmailsToday        int     `json:"emails_today"`
	EmailsThisWeek     int     `json:"emails_this_week"`
	HVACRelevantEmails int     `json:"hvac_relevant_emails"`
	PositiveSentiment  int     `json:"positive_sentiment"`
	NegativeSentiment  int     `json:"negative_sentiment"`
	AvgProcessingTime  time.Duration `json:"avg_processing_time"`
	TopKeywords        []string `json:"top_keywords"`
}

// 🤖 AI Performance
type AIPerformance struct {
	TotalRequests      int64         `json:"total_requests"`
	RequestsToday      int64         `json:"requests_today"`
	AvgResponseTime    time.Duration `json:"avg_response_time"`
	SuccessRate        float64       `json:"success_rate"`
	ModelAccuracy      float64       `json:"model_accuracy"`
	TokensProcessed    int64         `json:"tokens_processed"`
	ActiveModels       []string      `json:"active_models"`
	QueueLength        int           `json:"queue_length"`
}

// 🚨 Alert System
type Alert struct {
	ID          string    `json:"id"`
	Type        string    `json:"type"` // info, warning, error, critical
	Title       string    `json:"title"`
	Message     string    `json:"message"`
	Source      string    `json:"source"`
	Timestamp   time.Time `json:"timestamp"`
	Acknowledged bool     `json:"acknowledged"`
	Actions     []string  `json:"actions,omitempty"`
}

// ⚡ Quick Actions
type QuickAction struct {
	ID          string `json:"id"`
	Title       string `json:"title"`
	Description string `json:"description"`
	Icon        string `json:"icon"`
	Endpoint    string `json:"endpoint"`
	Method      string `json:"method"`
	Category    string `json:"category"`
	Dangerous   bool   `json:"dangerous,omitempty"`
}

// 📊 LangFuse-like Flow Tracking Structures

// FlowTracker tracks AI request flows like LangFuse
type FlowTracker struct {
	flows       map[string]*Flow
	flowsMutex  sync.RWMutex
	log         *log.Helper
}

// Flow represents a complete AI interaction flow
type Flow struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	UserID      string                 `json:"user_id,omitempty"`
	SessionID   string                 `json:"session_id,omitempty"`
	StartTime   time.Time              `json:"start_time"`
	EndTime     *time.Time             `json:"end_time,omitempty"`
	Duration    *time.Duration         `json:"duration,omitempty"`
	Status      string                 `json:"status"` // running, completed, failed
	Traces      []*Trace               `json:"traces"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	Tags        []string               `json:"tags,omitempty"`
	TotalCost   float64                `json:"total_cost"`
	TokenUsage  *TokenUsage            `json:"token_usage"`
}

// Trace represents a single step in the flow
type Trace struct {
	ID          string                 `json:"id"`
	FlowID      string                 `json:"flow_id"`
	ParentID    string                 `json:"parent_id,omitempty"`
	Name        string                 `json:"name"`
	Type        string                 `json:"type"` // llm, embedding, retrieval, tool
	StartTime   time.Time              `json:"start_time"`
	EndTime     *time.Time             `json:"end_time,omitempty"`
	Duration    *time.Duration         `json:"duration,omitempty"`
	Status      string                 `json:"status"` // running, completed, failed
	Input       interface{}            `json:"input,omitempty"`
	Output      interface{}            `json:"output,omitempty"`
	Error       string                 `json:"error,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	TokenUsage  *TokenUsage            `json:"token_usage,omitempty"`
	Cost        float64                `json:"cost"`
	Model       string                 `json:"model,omitempty"`
}

// TokenUsage tracks token consumption
type TokenUsage struct {
	InputTokens  int64 `json:"input_tokens"`
	OutputTokens int64 `json:"output_tokens"`
	TotalTokens  int64 `json:"total_tokens"`
}

// RequestMetrics tracks overall request metrics
type RequestMetrics struct {
	TotalRequests    int64                    `json:"total_requests"`
	SuccessfulRequests int64                  `json:"successful_requests"`
	FailedRequests   int64                    `json:"failed_requests"`
	AvgResponseTime  time.Duration            `json:"avg_response_time"`
	RequestsByModel  map[string]int64         `json:"requests_by_model"`
	RequestsByType   map[string]int64         `json:"requests_by_type"`
	LastUpdated      time.Time                `json:"last_updated"`
	mutex            sync.RWMutex
}

// TokenUsageTracker tracks token usage across all models
type TokenUsageTracker struct {
	TotalTokens      int64                    `json:"total_tokens"`
	TokensByModel    map[string]*TokenUsage   `json:"tokens_by_model"`
	TokensByUser     map[string]*TokenUsage   `json:"tokens_by_user"`
	TotalCost        float64                  `json:"total_cost"`
	CostByModel      map[string]float64       `json:"cost_by_model"`
	LastUpdated      time.Time                `json:"last_updated"`
	mutex            sync.RWMutex
}

// NewMorphicOctopusInterface creates the ultimate backend management interface
func NewMorphicOctopusInterface(
	db *gorm.DB,
	emailService *email.EmailIntelligenceService,
	transcriptionService *transcription.TranscriptionParser,
	customerService *customer.CustomerIntelligenceService,
	aiService *ai.Gemma3Service,
	config *OctopusConfig,
	logger log.Logger,
) *MorphicOctopusInterface {
	log := log.NewHelper(logger)

	octopus := &MorphicOctopusInterface{
		log:                  log,
		db:                   db,
		emailService:         emailService,
		transcriptionService: transcriptionService,
		customerService:      customerService,
		aiService:            aiService,
		config:               config,
		wsConnections:        make(map[string]*websocket.Conn),
		wsCleanupTicker:      time.NewTicker(30 * time.Second), // Cleanup every 30 seconds

		// 📊 Initialize LangFuse-like tracking
		flowTracker:          NewFlowTracker(logger),
		requestMetrics:       NewRequestMetrics(),
		tokenUsage:           NewTokenUsageTracker(),

		wsUpgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				return true // Allow all origins in development
			},
			ReadBufferSize:  1024,
			WriteBufferSize: 1024,
		},
	}

	// 🧹 Start WebSocket cleanup routine
	go octopus.startWebSocketCleanup()

	// 🌐 Setup HTTP server and routes
	octopus.setupHTTPServer()

	return octopus
}

// 🧹 WebSocket cleanup routine to prevent memory leaks
func (o *MorphicOctopusInterface) startWebSocketCleanup() {
	for range o.wsCleanupTicker.C {
		o.cleanupDeadConnections()
	}
}

// 🔍 Clean up dead WebSocket connections
func (o *MorphicOctopusInterface) cleanupDeadConnections() {
	o.wsConnectionsMutex.Lock()
	defer o.wsConnectionsMutex.Unlock()

	deadConnections := make([]string, 0)

	for connID, conn := range o.wsConnections {
		// Send ping to check if connection is alive
		if err := conn.WriteMessage(websocket.PingMessage, []byte{}); err != nil {
			deadConnections = append(deadConnections, connID)
			conn.Close()
		}
	}

	// Remove dead connections
	for _, connID := range deadConnections {
		delete(o.wsConnections, connID)
		o.log.Infof("🧹 Cleaned up dead WebSocket connection: %s", connID)
	}

	if len(deadConnections) > 0 {
		o.log.Infof("🧹 Cleaned up %d dead WebSocket connections", len(deadConnections))
	}
}

// 🔌 Add WebSocket connection with thread safety
func (o *MorphicOctopusInterface) addWebSocketConnection(connID string, conn *websocket.Conn) {
	o.wsConnectionsMutex.Lock()
	defer o.wsConnectionsMutex.Unlock()

	o.wsConnections[connID] = conn
	o.log.Infof("🔌 Added WebSocket connection: %s (total: %d)", connID, len(o.wsConnections))
}

// 🔌 Remove WebSocket connection with thread safety
func (o *MorphicOctopusInterface) removeWebSocketConnection(connID string) {
	o.wsConnectionsMutex.Lock()
	defer o.wsConnectionsMutex.Unlock()

	if conn, exists := o.wsConnections[connID]; exists {
		conn.Close()
		delete(o.wsConnections, connID)
		o.log.Infof("🔌 Removed WebSocket connection: %s (total: %d)", connID, len(o.wsConnections))
	}
}

// 📊 Get WebSocket connection count
func (o *MorphicOctopusInterface) getWebSocketConnectionCount() int {
	o.wsConnectionsMutex.RLock()
	defer o.wsConnectionsMutex.RUnlock()

	return len(o.wsConnections)
}

// Start starts the Octopus Interface HTTP server
func (o *MorphicOctopusInterface) Start(ctx context.Context) error {
	o.log.WithContext(ctx).Info("🚀 Starting Morphic Octopus Interface HTTP server...")

	// Start HTTP server in goroutine
	go func() {
		addr := fmt.Sprintf(":%d", o.config.HTTPPort)
		o.log.Infof("🌐 Octopus Interface listening on %s", addr)

		if err := o.httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			o.log.Errorf("HTTP server error: %v", err)
		}
	}()

	// Start flow cleanup routine
	go o.startFlowCleanup()

	o.log.WithContext(ctx).Info("✅ Morphic Octopus Interface started successfully")
	return nil
}

// Stop gracefully stops the Octopus Interface
func (o *MorphicOctopusInterface) Stop(ctx context.Context) error {
	o.log.WithContext(ctx).Info("🛑 Stopping Morphic Octopus Interface...")

	// Stop WebSocket cleanup ticker
	if o.wsCleanupTicker != nil {
		o.wsCleanupTicker.Stop()
	}

	// Close all WebSocket connections
	o.wsConnectionsMutex.Lock()
	for connID, conn := range o.wsConnections {
		conn.Close()
		o.log.Infof("🔌 Closed WebSocket connection: %s", connID)
	}
	o.wsConnections = make(map[string]*websocket.Conn)
	o.wsConnectionsMutex.Unlock()

	// Shutdown HTTP server
	if o.httpServer != nil {
		if err := o.httpServer.Shutdown(ctx); err != nil {
			return fmt.Errorf("failed to shutdown HTTP server: %w", err)
		}
	}

	o.log.WithContext(ctx).Info("✅ Morphic Octopus Interface stopped successfully")
	return nil
}

// startFlowCleanup starts the flow cleanup routine
func (o *MorphicOctopusInterface) startFlowCleanup() {
	ticker := time.NewTicker(1 * time.Hour) // Cleanup every hour
	defer ticker.Stop()

	for range ticker.C {
		// Cleanup flows older than 24 hours
		removed := o.flowTracker.CleanupOldFlows(24 * time.Hour)
		if removed > 0 {
			o.log.Infof("🧹 Cleaned up %d old flows", removed)
		}
	}
}

// setupHTTPServer configures the HTTP server and routes
func (o *MorphicOctopusInterface) setupHTTPServer() {
	router := mux.NewRouter()

	// 🐙 Octopus API routes
	api := router.PathPrefix("/api").Subrouter()

	// System health and status
	api.HandleFunc("/system/health", o.handleHealthCheck).Methods("GET")
	api.HandleFunc("/system/status", o.handleSystemStatus).Methods("GET")
	api.HandleFunc("/system/metrics", o.handleSystemMetrics).Methods("GET")

	// 📊 LangFuse-like Flow Tracking API
	api.HandleFunc("/flows", o.handleGetFlows).Methods("GET")
	api.HandleFunc("/flows/{flowId}", o.handleGetFlow).Methods("GET")
	api.HandleFunc("/flows/{flowId}/traces", o.handleGetFlowTraces).Methods("GET")
	api.HandleFunc("/flows/stats", o.handleGetFlowStats).Methods("GET")

	// 🔌 WebSocket endpoint for real-time updates
	api.HandleFunc("/dashboard/ws", o.handleWebSocket)

	// 📊 Dashboard data endpoints
	api.HandleFunc("/dashboard/data", o.handleDashboardData).Methods("GET")
	api.HandleFunc("/dashboard/alerts", o.handleDashboardAlerts).Methods("GET")
	api.HandleFunc("/dashboard/actions", o.handleQuickActions).Methods("GET")

	// 📧 Email intelligence endpoints
	api.HandleFunc("/email/stats", o.handleEmailStats).Methods("GET")
	api.HandleFunc("/email/analysis", o.handleEmailAnalysis).Methods("GET")

	// 🤖 AI performance endpoints
	api.HandleFunc("/ai/performance", o.handleAIPerformance).Methods("GET")
	api.HandleFunc("/ai/models", o.handleAIModels).Methods("GET")

	// 📞 Transcription endpoints
	api.HandleFunc("/transcription/stats", o.handleTranscriptionStats).Methods("GET")

	// 👥 Customer metrics endpoints
	api.HandleFunc("/customers/metrics", o.handleCustomerMetrics).Methods("GET")

	// 🌐 Static dashboard files (if needed)
	if o.config.DashboardPath != "" {
		router.PathPrefix(o.config.DashboardPath).Handler(
			http.StripPrefix(o.config.DashboardPath, http.FileServer(http.Dir("./web/octopus/"))),
		)
	}

	// Create HTTP server
	o.httpServer = &http.Server{
		Addr:         fmt.Sprintf(":%d", o.config.HTTPPort),
		Handler:      router,
		ReadTimeout:  15 * time.Second,
		WriteTimeout: 15 * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	o.log.Infof("🌐 HTTP server configured on port %d", o.config.HTTPPort)
}