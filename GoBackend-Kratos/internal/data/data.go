package data

import (
	"time"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gobackend-hvac-kratos/internal/conf"
)

// ProviderSet is data providers.
var ProviderSet = wire.NewSet(
	NewData,
	NewCustomerRepo,
	NewJobRepo,
	NewAIRepo,
	NewEmailRepo,
	NewDatabaseEmailStore,
	NewSimpleEmailService,
)

// Data represents the data layer
type Data struct {
	db  *gorm.DB
	log *log.Helper
}

// NewData creates a new data instance with optimized connection pool
func NewData(c *conf.Data, logger log.Logger) (*Data, func(), error) {
	log := log.NewHelper(logger)

	// 🚀 Optimized GORM configuration for high performance
	gormConfig := &gorm.Config{
		NowFunc: func() time.Time {
			return time.Now().UTC()
		},
		PrepareStmt:                              true,  // Cache prepared statements
		DisableForeignKeyConstraintWhenMigrating: false, // Keep FK constraints
		SkipDefaultTransaction:                   true,  // Skip default transactions for better performance
	}

	// Initialize database connection with optimized settings
	db, err := gorm.Open(postgres.Open(c.Database.Source), gormConfig)
	if err != nil {
		return nil, nil, err
	}

	// 🔧 Configure connection pool for optimal performance
	sqlDB, err := db.DB()
	if err != nil {
		return nil, nil, err
	}

	// 🏊‍♂️ Connection Pool Optimization
	sqlDB.SetMaxIdleConns(10)                  // Maximum idle connections
	sqlDB.SetMaxOpenConns(100)                 // Maximum open connections
	sqlDB.SetConnMaxLifetime(time.Hour)        // Connection max lifetime
	sqlDB.SetConnMaxIdleTime(30 * time.Minute) // Connection max idle time

	log.Info("🚀 Database connection pool configured: MaxIdle=10, MaxOpen=100, MaxLifetime=1h")

	// Auto-migrate database schema
	if err := db.AutoMigrate(
		&Customer{},
		&Job{},
		&Email{},
		&EmailAnalysis{},
		&EmailAttachment{},
	); err != nil {
		return nil, nil, err
	}

	d := &Data{
		db:  db,
		log: log,
	}

	cleanup := func() {
		log.Info("🔌 Closing database connection pool")
		if sqlDB, err := db.DB(); err == nil {
			sqlDB.Close()
		}
	}

	return d, cleanup, nil
}